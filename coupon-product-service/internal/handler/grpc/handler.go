package handler

import (
	"context"
	"math"

	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/service"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_product_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type ProductServer struct {
	proto_product_v1.UnimplementedProductServiceServer
	svc service.ProductService
}

func NewProductServer(svc service.ProductService) *ProductServer {
	return &ProductServer{svc: svc}
}

// Product operations

func (s *ProductServer) GetProduct(ctx context.Context, req *proto_product_v1.GetProductRequest) (*proto_product_v1.GetProductResponse, error) {
	product, err := s.svc.GetProductByID(ctx, req.ProductId)
	if err != nil {
		return &proto_product_v1.GetProductResponse{
			Error: errors.ToGRPCError(err),
		}, nil
	}

	return &proto_product_v1.GetProductResponse{
		Product: convertModelProductToProto(product),
	}, nil
}

func (s *ProductServer) UpdateProduct(ctx context.Context, req *proto_product_v1.UpdateProductRequest) (*proto_product_v1.UpdateProductResponse, error) {
	updateReq := &model.UpdateProductRequest{
		Name:          req.Name,
		Description:   req.Description,
		Price:         req.Price,
		CategoryID:    uint64PtrFromInt64(req.CategoryId),
		ImageURL:      stringPtrFromString(req.ImageUrl),
		StockQuantity: req.StockQuantity,
		Status:        convertProtoStatusToModel(req.Status),
		Brand:         stringPtrFromString(req.Brand),
		SKU:           stringPtrFromString(req.Sku),
	}

	product, err := s.svc.UpdateProduct(ctx, req.ProductId, updateReq)
	if err != nil {
		return &proto_product_v1.UpdateProductResponse{
			Error: errors.ToGRPCError(err),
		}, nil
	}

	return &proto_product_v1.UpdateProductResponse{
		Product: convertModelProductToProto(product),
	}, nil
}

func (s *ProductServer) ListProducts(ctx context.Context, req *proto_product_v1.ListProductsRequest) (*proto_product_v1.ListProductsResponse, error) {
	listReq := &model.ListProductsRequest{
		Page:       int(req.Pagination.Page),
		Limit:      int(req.Pagination.Limit),
		Search:     req.Search,
		CategoryID: uint64PtrFromInt64(req.CategoryId),
		Status:     convertProtoStatusToModel(req.Status),
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
	}

	products, total, err := s.svc.ListProducts(ctx, listReq)
	if err != nil {
		return &proto_product_v1.ListProductsResponse{
			Error: errors.ToGRPCError(err),
		}, nil
	}

	protoProducts := make([]*proto_product_v1.Product, len(products))
	for i, product := range products {
		protoProducts[i] = convertModelProductToProto(product)
	}

	totalPages := int32(math.Ceil(float64(total) / float64(listReq.Limit)))

	return &proto_product_v1.ListProductsResponse{
		Products: protoProducts,
		Pagination: &proto_common_v1.PaginationResponse{
			Page:       int32(listReq.Page),
			Limit:      int32(listReq.Limit),
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// Category operations

func (s *ProductServer) ListCategories(ctx context.Context, req *proto_product_v1.ListCategoriesRequest) (*proto_product_v1.ListCategoriesResponse, error) {
	listReq := &model.ListCategoriesRequest{
		Page:   int(req.Pagination.Page),
		Limit:  int(req.Pagination.Limit),
		Search: req.Search,
	}

	categories, total, err := s.svc.ListCategories(ctx, listReq)
	if err != nil {
		return &proto_product_v1.ListCategoriesResponse{
			Error: errors.ToGRPCError(err),
		}, nil
	}

	protoCategories := make([]*proto_product_v1.Category, len(categories))
	for i, category := range categories {
		protoCategories[i] = convertModelCategoryToProto(category)
	}

	totalPages := int32(math.Ceil(float64(total) / float64(listReq.Limit)))

	return &proto_product_v1.ListCategoriesResponse{
		Categories: protoCategories,
		Pagination: &proto_common_v1.PaginationResponse{
			Page:       int32(listReq.Page),
			Limit:      int32(listReq.Limit),
			Total:      total,
			TotalPages: totalPages,
		},
	}, nil
}

// Health check
func (s *ProductServer) HealthCheck(ctx context.Context, req *proto_common_v1.HealthCheckRequest) (*proto_common_v1.HealthCheckResponse, error) {
	return &proto_common_v1.HealthCheckResponse{
		Status: proto_common_v1.HealthStatus_HEALTH_STATUS_SERVING,
	}, nil
}

// Helper functions
func stringPtrFromString(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

func stringFromStringPtr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func uint64PtrFromInt64(i int64) *uint64 {
	if i == 0 {
		return nil
	}
	u := uint64(i)
	return &u
}

func int64FromUint64Ptr(u *uint64) int64 {
	if u == nil {
		return 0
	}
	return int64(*u)
}

func convertProtoStatusToModel(status proto_product_v1.ProductStatus) model.ProductStatus {
	switch status {
	case proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE:
		return model.ProductStatusActive
	case proto_product_v1.ProductStatus_PRODUCT_STATUS_INACTIVE:
		return model.ProductStatusInactive
	default:
		return model.ProductStatusActive
	}
}

func convertModelStatusToProto(status model.ProductStatus) proto_product_v1.ProductStatus {
	switch status {
	case model.ProductStatusActive:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE
	case model.ProductStatusInactive:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_INACTIVE
	default:
		return proto_product_v1.ProductStatus_PRODUCT_STATUS_ACTIVE
	}
}

func convertModelProductToProto(product *model.Product) *proto_product_v1.Product {
	protoProduct := &proto_product_v1.Product{
		Id:            int64(product.ID),
		Name:          product.Name,
		Description:   product.Description,
		Price:         product.Price,
		CategoryId:    int64FromUint64Ptr(product.CategoryID),
		ImageUrl:      stringFromStringPtr(product.ImageURL),
		StockQuantity: product.StockQuantity,
		Status:        convertModelStatusToProto(product.Status),
		Brand:         stringFromStringPtr(product.Brand),
		Sku:           stringFromStringPtr(product.SKU),
		CreatedAt:     timestamppb.New(product.CreatedAt),
		UpdatedAt:     timestamppb.New(product.UpdatedAt),
	}

	if product.Category != nil {
		protoProduct.Category = convertModelCategoryToProto(product.Category)
	}

	return protoProduct
}

func convertModelCategoryToProto(category *model.Category) *proto_product_v1.Category {
	return &proto_product_v1.Category{
		Id:          int64(category.ID),
		Name:        category.Name,
		Description: category.Description,
		CreatedAt:   timestamppb.New(category.CreatedAt),
	}
}
