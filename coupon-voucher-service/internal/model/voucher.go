package model

import "time"

type UsageMethod string

const (
	UsageMethodManual    UsageMethod = "MANUAL"
	UsageMethodAutomatic UsageMethod = "AUTO"
)

type VoucherStatus string

const (
	VoucherStatusActive   VoucherStatus = "ACTIVE"
	VoucherStatusInactive VoucherStatus = "INACTIVE"
	VoucherStatusExpired  VoucherStatus = "EXPIRED"
)

type TimeRestrictionType string

const (
	TimeRestrictionTypeDaysOfWeek     TimeRestrictionType = "DAYS_OF_WEEK"
	TimeRestrictionTypeHoursOfDay     TimeRestrictionType = "HOURS_OF_DAY"
	TimeRestrictionTypeSpecificDates  TimeRestrictionType = "SPECIFIC_DATES"
	TimeRestrictionTypeRecurringDates TimeRestrictionType = "RECURRING_DATES"
)

type RecurrencePattern string

const (
	RecurrencePatternDaily     RecurrencePattern = "DAILY"
	RecurrencePatternWeekly    RecurrencePattern = "WEEKLY"
	RecurrencePatternMonthly   RecurrencePattern = "MONTHLY"
	RecurrencePatternQuarterly RecurrencePattern = "QUARTERLY"
	RecurrencePatternYearly    RecurrencePattern = "YEARLY"
)

type DiscountType struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	TypeCode    string    `gorm:"type:varchar(50);not null;unique" json:"type_code"`
	TypeName    string    `gorm:"type:varchar(255);not null" json:"type_name"`
	Description string    `json:"description"`
	IsActive    bool      `gorm:"not null;default:true" json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type Voucher struct {
	ID                uint          `gorm:"primaryKey;autoIncrement" json:"id"`
	VoucherCode       string        `gorm:"type:varchar(255);not null;unique" json:"voucher_code"`
	Title             string        `gorm:"type:varchar(255);not null" json:"title"`
	Description       string        `json:"description"`
	DiscountTypeID    uint          `gorm:"not null" json:"discount_type_id"`
	DiscountValue     float64       `gorm:"not null" json:"discount_value"`
	UsageMethod       UsageMethod   `gorm:"type:varchar(50);not null" json:"usage_method"`
	ValidFrom         time.Time     `gorm:"not null" json:"valid_from"`
	ValidUntil        time.Time     `gorm:"not null" json:"valid_until"`
	MaxUsageCount     *int          `json:"max_usage_count"`
	MaxUsagePerUser   *int          `json:"max_usage_per_user"`
	UserEligibility   string        `gorm:"type:varchar(100);not null;default:'ALL'" json:"user_eligibility_type"`
	CurrentUsageCount int           `gorm:"not null;default:0" json:"current_usage_count"`
	MinOrderAmount    float64       `gorm:"not null;default:0" json:"min_order_amount"`
	MaxDiscountAmount *float64      `json:"max_discount_amount"`
	CreatedBy         uint          `gorm:"not null" json:"created_by"`
	Status            VoucherStatus `gorm:"type:varchar(50);not null;default:'ACTIVE'" json:"status"`
	CreatedAt         time.Time     `json:"created_at"`
	UpdatedAt         time.Time     `json:"updated_at"`

	DiscountType         *DiscountType                `gorm:"-" json:"discount_type,omitempty"`
	ProductRestrictions  []*VoucherProductRestriction `gorm:"-" json:"product_restrictions,omitempty"`
	TimeRestrictions     []*VoucherTimeRestriction    `gorm:"-" json:"time_restrictions,omitempty"`
	UserEligibilityRules []*VoucherUserEligibility    `gorm:"-" json:"user_eligibility,omitempty"`
	UserUsage            []*UserVoucherUsage          `gorm:"-" json:"user_usage,omitempty"`
	TotalSavings         float64                      `gorm:"-" json:"total_savings"`
	UniqueUsers          int                          `gorm:"-" json:"unique_users"`
}

type VoucherProductRestriction struct {
	ID           uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	VoucherID    uint      `gorm:"not null" json:"voucher_id"`
	ProductID    *uint     `json:"product_id,omitempty"`
	CategoryID   *uint     `json:"category_id,omitempty"`
	ProductName  *string   `gorm:"-" json:"product_name,omitempty"`
	CategoryName *string   `gorm:"-" json:"category_name,omitempty"`
	IsIncluded   bool      `gorm:"not null" json:"is_included"`
}

type VoucherTimeRestriction struct {
	ID                   uint                `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt            time.Time           `json:"created_at"`
	VoucherID            uint                `gorm:"not null" json:"voucher_id"`
	RestrictionType      TimeRestrictionType `gorm:"type:varchar(50);not null" json:"restriction_type"`
	AllowedDaysOfWeek    []int32             `gorm:"type:integer[]" json:"allowed_days_of_week"`
	AllowedHoursStart    *int32              `json:"allowed_hours_start,omitempty"`
	AllowedHoursEnd      *int32              `json:"allowed_hours_end,omitempty"`
	SpecificDates        []time.Time         `gorm:"type:date[]" json:"specific_dates"`
	RecurrencePattern    *RecurrencePattern  `gorm:"type:varchar(50)" json:"recurrence_pattern,omitempty"`
	RecurrenceDayOfMonth *int32              `json:"recurrence_day_of_month,omitempty"`
	RecurrenceMonth      *int32              `json:"recurrence_month,omitempty"`
	RecurrenceDayOfWeek  *int32              `json:"recurrence_day_of_week,omitempty"`
}

type VoucherUserEligibility struct {
	ID                uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt         time.Time `json:"created_at"`
	VoucherID         uint      `gorm:"not null" json:"voucher_id"`
	UserID            *uint     `json:"user_id,omitempty"`
	UserType          *string   `gorm:"type:varchar(50)" json:"user_type,omitempty"`
	MinAccountAgeDays *int32    `json:"min_account_age_days,omitempty"`
	MaxAccountAgeDays *int32    `json:"max_account_age_days,omitempty"`
	MinPreviousOrders *int64    `json:"min_previous_orders,omitempty"`
	MaxPreviousOrders *int64    `json:"max_previous_orders,omitempty"`
}

type VoucherOrderUsage struct {
	OrderID     string    `json:"order_id"`
	UsedAt      time.Time `json:"used_at"`
	OrderAmount float64   `json:"order_amount"`
	Status      string    `json:"status"`
}

type UserVoucherUsage struct {
	UserID     string               `json:"user_id"`
	UsageCount int                  `json:"usage_count"`
	FullName   string               `json:"full_name"`
	Email      string               `json:"email"`
	Type       string               `json:"type"`
	Orders     []*VoucherOrderUsage `json:"orders,omitempty"`
}

type CartItem struct {
	ProductID  *uint   `json:"product_id,omitempty"`
	CategoryID *uint   `json:"category_id,omitempty"`
	Quantity   int     `json:"quantity"`
	Price      float64 `json:"price"`
}

type CreateVoucherRequest struct {
	VoucherCode       string      `json:"voucher_code" validate:"required,min=3,max=50"`
	Title             string      `json:"title" validate:"required,min=1,max=255"`
	Description       string      `json:"description"`
	DiscountTypeID    uint        `json:"discount_type_id" validate:"required"`
	DiscountValue     float64     `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       UsageMethod `json:"usage_method" validate:"required"`
	ValidFrom         time.Time   `json:"valid_from" validate:"required"`
	ValidUntil        time.Time   `json:"valid_until" validate:"required"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	MinOrderAmount    float64     `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
}

type UpdateVoucherRequest struct {
	Title             string      `json:"title" validate:"required,min=1,max=255"`
	Description       string      `json:"description"`
	DiscountTypeID    uint        `json:"discount_type_id" validate:"required"`
	DiscountValue     float64     `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       UsageMethod `json:"usage_method" validate:"required"`
	Status            string      `json:"status" validate:"required"`
	MinOrderAmount    float64     `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	ValidFrom         time.Time   `json:"valid_from" validate:"required"`
	ValidUntil        time.Time   `json:"valid_until" validate:"required"`

	ProductRestrictions []*VoucherProductRestriction `json:"product_restrictions"`
	TimeRestrictions    []*VoucherTimeRestriction    `json:"time_restrictions"`
	UserEligibility     []*VoucherUserEligibility    `json:"user_eligibility"`
}

type ListVouchersRequest struct {
	Page           int          `json:"page" query:"page"`
	Limit          int          `json:"limit" query:"limit"`
	Search         string       `json:"search" query:"search"`
	DiscountTypeID *string      `json:"discount_type_id" query:"discount_type_id"`
	UsageMethod    *UsageMethod `json:"usage_method" query:"usage_method"`
	Status         string       `json:"status" query:"status"`
	SortBy         string       `json:"sort_by" query:"sort_by"`
	SortOrder      string       `json:"sort_order" query:"sort_order"`
}

type PaginatedResponse[T any] struct {
	Data       []T `json:"data"`
	Total      int `json:"total"`
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	TotalPages int `json:"total_pages"`
}

type VoucherEligibilityRequest struct {
	VoucherCode    string     `json:"voucher_code" validate:"required"`
	UserID         uint       `json:"user_id" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time  `json:"order_timestamp"`
	CartItems      []CartItem `json:"cart_items"`
}

type VoucherEligibilityResponse struct {
	Eligible       bool    `json:"eligible"`
	Message        string  `json:"message"`
	VoucherID      *uint   `json:"voucher_id,omitempty"`
	DiscountAmount float64 `json:"discount_amount"`
}

type AutoVoucherEligibilityRequest struct {
	UserID         uint       `json:"user_id" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time  `json:"order_timestamp"`
	CartItems      []CartItem `json:"cart_items"`
}

type EligibleVoucher struct {
	Eligible       bool     `json:"eligible"`
	Voucher        *Voucher `json:"voucher"`
	DiscountAmount float64  `json:"discount_amount"`
}

func (Voucher) TableName() string {
	return "vouchers"
}

func (DiscountType) TableName() string {
	return "discount_types"
}

func (VoucherProductRestriction) TableName() string {
	return "voucher_product_restrictions"
}

func (VoucherTimeRestriction) TableName() string {
	return "voucher_time_restrictions"
}

func (VoucherUserEligibility) TableName() string {
	return "voucher_user_eligibility"
}

func GetAllModels() []any {
	return []any{
		&Voucher{},
		&DiscountType{},
		&VoucherProductRestriction{},
		&VoucherTimeRestriction{},
		&VoucherUserEligibility{},
	}
}
